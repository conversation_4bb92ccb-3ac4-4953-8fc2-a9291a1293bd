import { deleteProposal, getCurrentUserProposals } from '@/api/proposal.api';
import { AppConfirm } from '@/components/AppConfirm';
import { AppStatus } from '@/components/AppStatus';
import { ProposalModal } from '@/components/proposal-modal';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useRootContext } from '@/context/useRootContext';
import { AppProposal } from '@/core.types';
import { Edit2, Trash2 } from 'lucide-react';
import { useEffect, useState } from 'react';

export const ProposalsTab = () => {
  const [proposals, setProposals] = useState<AppProposal[]>([]);
  const { isAuthorized } = useRootContext();

  const onDeleteConfirm = async (proposalId) => {
    await deleteProposal(proposalId);
    setProposals((prev) => prev.filter((r) => r.id !== proposalId));
  };

  const onProposalUpdated = (updatedProposal) => {
    setProposals((prev) =>
      prev.map((r) => {
        if (r.id === updatedProposal.id) {
          return updatedProposal;
        }
        return r;
      }),
    );
  };

  useEffect(() => {
    const fetchProposals = async () => {
      const userProposals = await getCurrentUserProposals();
      setProposals(userProposals);
    };

    if (isAuthorized) {
      fetchProposals();
    }
  }, [isAuthorized]);

  return (
    <div className='p-4'>
      <Table>
        <TableCaption>A list of your recent proposals.</TableCaption>
        <TableHeader>
          <TableRow>
            <TableHead>Title</TableHead>
            <TableHead>Created At</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Comment</TableHead>
            <TableHead>Reject reason</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {proposals.map((proposal) => {
            const { title, url, createdAt, status, comment, rejectReason, id } =
              proposal;
            return (
              <TableRow key={title}>
                <TableCell className='font-medium'>
                  <a href={url} target='_blank' className='underline'>
                    {title}
                  </a>
                </TableCell>
                <TableCell>
                  {new Date(createdAt).toLocaleDateString()}
                </TableCell>
                <TableCell>
                  <AppStatus status={status} />
                </TableCell>
                <TableCell>{comment}</TableCell>
                <TableCell>{rejectReason ?? 'None'}</TableCell>
                <TableCell>
                  <div className='flex gap-2'></div>
                  <AppConfirm
                    onConfirm={() => onDeleteConfirm(id)}
                    message='Are you sure to Delete this proposal?'
                  >
                    <Button variant='ghost' size='icon'>
                      <Trash2 className='h-4 w-4' />
                    </Button>
                  </AppConfirm>
                  <ProposalModal
                    mode='edit'
                    onSubmitCompleted={onProposalUpdated}
                    defaultValues={proposal}
                    trigger={
                      <Button variant='ghost' size='icon'>
                        <Edit2 className='h-4 w-4' />
                      </Button>
                    }
                  />
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
};
