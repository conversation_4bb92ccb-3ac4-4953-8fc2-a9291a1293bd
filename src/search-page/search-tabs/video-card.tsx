import { AppAuthorNameAvatar } from '@/components/AppAuthorNameAvatar';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { useRootContext } from '../../context/useRootContext';
import { AppResource, AppTag } from '../../core.types';
import { firebaseTimestampToFriendlyDate } from '../../services/utils-service';
import { BookmarkAction } from '../resource-actions/bookmark-action';
import { DislikeAction } from '../resource-actions/dislike-action';
import { LikeAction } from '../resource-actions/like-action';
import { ReadMarkAction } from '../resource-actions/readmark-action';
import { ResourceTags } from './resource-tags';
import { DeleteAction } from '../resource-actions/delete-action';
import { EditAction } from '../resource-actions/edit-action';
import { GlowingEffect } from '@/components/ui/glowing-effect';
interface VideoTileProps {
  resource: AppResource;
}

export const VideoCard = ({ resource }: VideoTileProps) => {
  const embedUrl = resource.url.replace('watch?v=', 'embed/');
  const { authors, tags } = useRootContext();

  const author = authors.find((author) => author.id === resource.authorId);

  const tagsList = resource.tags.map((tagId) =>
    tags.find((tag) => tag.id === tagId),
  ) as AppTag[];

  return (
    <Card className='relative flex flex-col cursor-pointer'>
      {/* Glowing Effect */}
      <GlowingEffect disabled={false} />
      <div className='flex justify-end f-full pt-2'>
        <span className='font-semibold whitespace-pre-wrap text-black dark:text-[#d4d1d1] flex gap-2 items-center text-[12px] mr-auto ml-6'>
          {firebaseTimestampToFriendlyDate(resource?.createdAt as any)}
        </span>
        <LikeAction {...{ resource }} />
        <DislikeAction {...{ resource }} />
        <BookmarkAction appResourceId={resource.id} />
        <ReadMarkAction appResourceId={resource.id} />
        {/* TODO edit action */}
      </div>
      <div className='py-4 pl-6'>
        <AppAuthorNameAvatar
          author={author}
          nameLength={130}
          customTwitterId={resource.customTwitterId}
        />
      </div>

      <CardContent>
        <div className='min-w-[300px] grow'>
          <h1 className='text-lg'>{resource.title}</h1>
          <iframe
            src={embedUrl}
            title='YouTube video'
            allowFullScreen
            className='w-full h-full min-h-[300px] py-3'
          />
          <div className='mt-2 text-[13px] text-right'>
            Have problem with this video? Click{' '}
            <a href={resource.url} target='_blank' className='underline'>
              here
            </a>
          </div>
        </div>
      </CardContent>
      <CardFooter className='grow'>
        <ResourceTags tags={tagsList} className='mt-auto' />
      </CardFooter>
      <div className='flex justify-end gap-2 py-2 w-full'>
        <EditAction {...{ resource }} />
        <DeleteAction resourceId={resource.id} />
      </div>
    </Card>
  );
};
