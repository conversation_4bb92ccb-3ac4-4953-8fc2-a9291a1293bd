import { useAppResourcesContext } from '../app-resources-context';
import { GithubCard } from './github-card';

export const GithubTab = () => {
  const { loadingMarkup, resources } = useAppResourcesContext();

  return (
    <div className='max-w-7xl mx-auto p-2'>
      <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2'>
        {resources.map((resource) => (
          <GithubCard
            key={resource.id}
            {...{
              resource,
            }}
          />
        ))}
      </div>
      {loadingMarkup}
    </div>
  );
};
