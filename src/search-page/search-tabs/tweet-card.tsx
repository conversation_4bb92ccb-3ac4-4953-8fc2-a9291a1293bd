import { AppResource, AppTag } from '../../core.types';

import { <PERSON>, CardContent, CardFooter } from '@/components/ui/card';
import { cn } from '@/utils';
import { Tweet } from 'react-tweet';
import { useLocalStorage } from 'usehooks-ts';
import { useRootContext } from '../../context/useRootContext';
import { ExternalLinkIcon } from 'lucide-react';
import { BookmarkAction } from '../resource-actions/bookmark-action';
import { DeleteAction } from '../resource-actions/delete-action';
import { DislikeAction } from '../resource-actions/dislike-action';
import { EditAction } from '../resource-actions/edit-action';
import { LikeAction } from '../resource-actions/like-action';
import { ReadMarkAction } from '../resource-actions/readmark-action';
import { ResourceTags } from './resource-tags';
import { GlowingEffect } from '@/components/ui/glowing-effect';

type TweetCardProps = {
  resource: AppResource;
  className?: string;
};

export const TweetCard = ({ resource, className }: TweetCardProps) => {
  const { tags } = useRootContext();
  const [isDarkMode] = useLocalStorage<boolean>('usehooks-ts-dark-mode', false);

  const tagsList = resource.tags
    .map((tagId) => tags.find((tag) => tag.id === tagId))
    .filter(Boolean) as AppTag[];

  const tweetId = resource.url.split('/').pop();

  // Create a direct link to the tweet
  const tweetUrl = `https://twitter.com/i/web/status/${tweetId}`;

  return (
    <Card
      className={cn(
        'relative flex flex-col overflow-hidden transition-all duration-200 hover:shadow-md',
        'border border-border/60 hover:border-border/80',

        className,
      )}
    >
      {/* Glowing Effect */}
      <GlowingEffect
        spread={40}
        glow={true}
        disabled={false}
        proximity={64}
        inactiveZone={0.01}
        borderWidth={2}
      />
      <div className='relative'>
        <div
          className={cn(
            'p-3 pb-0 text-sm [&_*]:text-sm [&_p]:text-sm [&_span]:text-sm',
            {
              dark: isDarkMode,
              light: !isDarkMode,
            },
          )}
        >
          <Tweet id={tweetId as string} />
        </div>

        <div className='absolute top-2 right-2 flex gap-1 p-1 rounded-full bg-background/80 backdrop-blur-sm border border-border/40'>
          <LikeAction resource={resource} />
          <DislikeAction resource={resource} />
          <BookmarkAction appResourceId={resource.id} />
        </div>
      </div>

      <div className='flex flex-col mt-auto'>
        <CardContent className='pt-0 pb-2'>
          <ResourceTags tags={tagsList} className='mt-2' />
        </CardContent>

        <CardFooter className='pt-0 pb-3 px-3 flex justify-between items-center border-t border-border/20'>
          <div className='flex items-center gap-1'>
            <a
              href={tweetUrl}
              target='_blank'
              rel='noopener noreferrer'
              className='p-1.5 text-muted-foreground hover:text-foreground rounded-full hover:bg-accent transition-colors'
            >
              <ExternalLinkIcon className='h-4 w-4' />
            </a>
            <div className='p-1.5 text-muted-foreground hover:text-foreground rounded-full hover:bg-accent transition-colors'>
              <ReadMarkAction appResourceId={resource.id} />
            </div>
          </div>

          <div className='flex items-center gap-1'>
            <EditAction resource={resource} />
            <DeleteAction resourceId={resource.id} />
          </div>
        </CardFooter>
      </div>
    </Card>
  );
};
