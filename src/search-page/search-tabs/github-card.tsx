import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { cn } from '@/utils';
import { useLocalStorage } from 'usehooks-ts';
import { useRootContext } from '../../context/useRootContext';
import { AppResource, AppTag } from '../../core.types';
import {
  firebaseTimestampToFriendlyDate,
  getUserNameRepoFromUrl,
} from '../../services/utils-service';

import { BookmarkAction } from '../resource-actions/bookmark-action';
import { DislikeAction } from '../resource-actions/dislike-action';
import { LikeAction } from '../resource-actions/like-action';
import { ReadMarkAction } from '../resource-actions/readmark-action';
import { ResourceTags } from './resource-tags';
import { AppAuthorNameAvatar } from '@/components/AppAuthorNameAvatar';
import { Repository } from '../../components/react-github-embed/lib/';
import { DeleteAction } from '../resource-actions/delete-action';
import { EditAction } from '../resource-actions/edit-action';

interface GithubCardProps {
  resource: AppResource;
}

export const GithubCard = ({ resource }: GithubCardProps) => {
  const { userName, repoName } = getUserNameRepoFromUrl(resource.url);
  const { authors, tags } = useRootContext();

  const [isDarkMode] = useLocalStorage<boolean>('usehooks-ts-dark-mode', false);

  const author = authors.find((author) => author.id === resource.authorId);

  const tagsList = resource.tags.map((tagId) =>
    tags.find((tag) => tag.id === tagId),
  ) as AppTag[];

  return (
    <Card className='flex flex-col cursor-pointer'>
      <div className='flex justify-end f-full pt-2'>
        <span className='font-semibold whitespace-pre-wrap text-black dark:text-[#d4d1d1] flex gap-2 items-center text-[12px] mr-auto ml-6'>
          {firebaseTimestampToFriendlyDate(resource?.createdAt as any)}
        </span>
        <LikeAction {...{ resource }} />
        <DislikeAction {...{ resource }} />
        <BookmarkAction appResourceId={resource.id} />
        <ReadMarkAction appResourceId={resource.id} />
      </div>
      <div className='py-4 pl-6'>
        <AppAuthorNameAvatar
          author={author}
          nameLength={130}
          customTwitterId={resource.customTwitterId}
        />
      </div>
      <CardContent>
        <div
          className={cn('flex justify-center', {
            dark: isDarkMode,
            light: !isDarkMode,
          })}
        >
          <Repository
            username={userName}
            repository={repoName}
            theme={isDarkMode ? 'dark' : 'light'}
          />
        </div>
      </CardContent>
      <CardFooter className='grow'>
        <ResourceTags tags={tagsList} className='mt-auto' />
      </CardFooter>
      <div className='flex justify-end gap-2 py-2 w-full'>
        <EditAction {...{ resource }} />
        <DeleteAction resourceId={resource.id} />
      </div>
    </Card>
  );
};
