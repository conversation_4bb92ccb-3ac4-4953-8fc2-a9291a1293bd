import { AppAuthorNameAvatar } from '@/components/AppAuthorNameAvatar';
import { useRootContext } from '@/context/useRootContext';
import { AppResource, AppTag } from '@/core.types';
import { firebaseTimestampToFriendlyDate } from '@/services/utils-service';
import { cn } from '@/utils';
import { BookmarkAction } from '../resource-actions/bookmark-action';
import { DislikeAction } from '../resource-actions/dislike-action';
import { LikeAction } from '../resource-actions/like-action';
import { ReadMarkAction } from '../resource-actions/readmark-action';
import { ResourceTags } from './resource-tags';
import { DeleteAction } from '../resource-actions/delete-action';
import { EditAction } from '../resource-actions/edit-action';
import { GlowingEffect } from '@/components/ui/glowing-effect';

export const ArticleCard = ({ resource }: { resource: AppResource }) => {
  const { tags, authors } = useRootContext();

  const author = authors.find((author) => author.id === resource.authorId);
  const tagsList = resource.tags
    .map((tagId) => tags.find((tag) => tag.id === tagId))
    .filter(Boolean) as AppTag[];

  return (
    <div
      key={resource.id}
      className='relative flex flex-col items-start gap-2 rounded-lg border p-3 text-left text-sm transition-all hover:bg-accent max-h-[400px]'
    >
      {/* Glowing Effect */}
      <GlowingEffect
        spread={40}
        glow={true}
        disabled={false}
        proximity={64}
        inactiveZone={0.01}
        borderWidth={2}
      />
      <div className='w-full'>
        <div className='flex gap-2 justify-between w-full items-center mb-2 flex-wrap'>
          <AppAuthorNameAvatar
            author={author}
            nameLength={130}
            customTwitterId={resource.customTwitterId}
          />

          <div className='flex items-center gap-2 flex-wrap'>
            <LikeAction {...{ resource }} />
            <DislikeAction {...{ resource }} />
            <BookmarkAction appResourceId={resource.id} />
            <ReadMarkAction appResourceId={resource.id} />
          </div>
        </div>

        <div className='flex items-start justify-between'>
          <div className='flex items-center gap-2'>
            <a
              href={resource.url}
              target='_blank'
              rel='noopener noreferrer'
              className='font-semibold hover:underline cursor-pointer'
            >
              {resource.title}
            </a>
          </div>
          <div
            className={cn(
              'ml-auto text-xs text-right mr-3',
              'text-muted-foreground min-w-[100px]',
            )}
          >
            {/* @ts-expect-error note */}
            {firebaseTimestampToFriendlyDate(resource.createdAt)}
          </div>
        </div>
      </div>
      <ResourceTags tags={tagsList} />
      <div className='flex justify-end gap-2 py-2 w-full'>
        <EditAction {...{ resource }} />
        <DeleteAction resourceId={resource.id} />
      </div>
    </div>
  );
};
