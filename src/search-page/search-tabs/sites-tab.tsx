import { useAppResourcesContext } from '../app-resources-context';
import { SiteCard } from './site-card';
import { ResourceTable } from '@/components/ResourceTable';
import { useGlobalView } from '@/context/GlobalViewContext';

export const SitesTab = () => {
  const { loadingMarkup, resources, loading } = useAppResourcesContext();
  const { globalViewMode } = useGlobalView();

  return (
    <div style={{ height: 'calc(100vh - 117px)', overflowY: 'auto' }}>
      {globalViewMode === 'table' ? (
        <div className='p-4'>
          <ResourceTable
            resources={resources}
            loading={loading}
            loadingMarkup={loadingMarkup}
            showViewToggle={false}
          />
        </div>
      ) : (
        <div className='max-w-7xl mx-auto p-2'>
          <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2'>
            {resources.map((resource) => (
              <SiteCard
                key={resource.id}
                {...{
                  resource,
                }}
              />
            ))}
          </div>
          {loadingMarkup}
        </div>
      )}
    </div>
  );
};
