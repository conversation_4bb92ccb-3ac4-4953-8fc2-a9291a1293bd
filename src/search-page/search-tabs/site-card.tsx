import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { useRootContext } from '../../context/useRootContext';
import { AppResource, AppTag } from '../../core.types';
import { firebaseTimestampToFriendlyDate } from '../../services/utils-service';
import { AppButtonSecondary } from '@/components/buttons/app-button-new';
import { BookmarkAction } from '../resource-actions/bookmark-action';
import { DeleteAction } from '../resource-actions/delete-action';
import { DislikeAction } from '../resource-actions/dislike-action';
import { EditAction } from '../resource-actions/edit-action';
import { LikeAction } from '../resource-actions/like-action';
import { ReadMarkAction } from '../resource-actions/readmark-action';
import { ResourceTags } from './resource-tags';
import { useState } from 'react';

interface SiteCardProps {
  resource: AppResource;
}

export const SiteCard = ({ resource }: SiteCardProps) => {
  const { tags } = useRootContext();
  const [imageError, setImageError] = useState(false);

  const tagsList = resource.tags.map((tagId) =>
    tags.find((tag) => tag.id === tagId),
  ) as AppTag[];

  const handleImageError = () => {
    setImageError(true);
  };

  return (
    <Card className='flex flex-col cursor-pointer shadow-sm hover:shadow-md transition-shadow w-full'>
      {/* Header: title + meta + compact actions */}
      <div className='flex items-start gap-3 p-4 border-b bg-transparent'>
        <div className='flex-1 min-w-0'>
          <h2 className='text-lg font-semibold leading-tight text-ellipsis'>
            {resource.title}
          </h2>
          <div className='text-[12px] text-muted-foreground mt-1'>
            {firebaseTimestampToFriendlyDate(resource?.createdAt as any)}
          </div>
        </div>

        <div className='flex items-center gap-2'>
          <LikeAction {...{ resource }} />
          <DislikeAction {...{ resource }} />
          <BookmarkAction appResourceId={resource.id} />
          <ReadMarkAction appResourceId={resource.id} />
        </div>
      </div>

      {/* Main: image + description (stack on small, row on md+) */}
      <CardContent className='p-4'>
        <div className='flex flex-col md:flex-row items-start gap-4'>
          {/* Image: full width on small, fixed square on md+ */}
          <div className='w-full md:w-40 h-40 md:h-40 rounded-md bg-slate-100 overflow-hidden flex-shrink-0 flex items-center justify-center'>
            {!imageError && resource.imageUrl ? (
              <img
                src={resource.imageUrl}
                alt={resource.title || 'resource image'}
                className='w-full h-full object-cover'
                onError={handleImageError}
              />
            ) : (
              <div className='flex items-center justify-center w-full h-full bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-800 dark:to-gray-900'>
                <img
                  src={'/src/icons/logo.png'}
                  alt='logo'
                  className='w-20 h-20 object-contain'
                />
              </div>
            )}
          </div>

          {/* Content: description + visit button */}
          <div className='flex-1 min-w-0'>
            <p className='text-sm text-foreground leading-relaxed whitespace-pre-wrap break-words'>
              {resource.description}
            </p>

            <div className='mt-4 flex items-center gap-3'>
              <AppButtonSecondary
                onClick={() => window.open(resource.url, '_blank')}
              >
                Visit
              </AppButtonSecondary>

              {/* show edit/delete inline on larger screens */}
              <div className='ml-auto flex gap-2'>
                <EditAction {...{ resource }} />
                <DeleteAction resourceId={resource.id} />
              </div>
            </div>
          </div>
        </div>
      </CardContent>

      {/* Footer: tags */}
      <CardFooter className='pt-0 px-4 pb-4'>
        <ResourceTags tags={tagsList} className='flex-wrap' />
      </CardFooter>
    </Card>
  );
};
