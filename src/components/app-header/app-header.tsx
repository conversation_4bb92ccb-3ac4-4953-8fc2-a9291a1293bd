import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useRootContext } from '@/context/useRootContext';
import { CircleUserIcon, MenuIcon, Plus } from 'lucide-react';
import { useCallback } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import logo from '../../icons/logo.png';
import { AppTheme } from '../AppTheme';
import { AppButtonSecondary } from '../buttons/app-button-new';
import { ProposalModal } from '../proposal-modal';
import { Sheet, SheetContent, SheetTrigger } from '../ui/sheet';
import { WelcomeModal } from '../welcome-modal';
import { ShareButton } from './share-button';
import { SignInButtons } from './sign-in-buttons';

export const AppHeader = () => {
  const navigate = useNavigate();
  const { pathname } = useLocation();
  const navigateTo = useCallback(
    (path: string) => {
      if (pathname === path) return;
      navigate(path);
    },
    [navigate, pathname],
  );

  const { isAuthorized, logout, hasAdminRole } = useRootContext();

  const onLogoClick = () => {
    navigate('/');
  };

  const addResourceButton = (
    <AppButtonSecondary>
      <Plus className='cursor-pointer' strokeWidth={2} />
      Add resource
    </AppButtonSecondary>
  );

  return (
    <header className='flex h-16 items-center gap-4 border-b bg-background px-4 md:px-6'>
      <Sheet>
        <SheetTrigger asChild>
          <Button variant='outline' size='icon' className='shrink-0 md:hidden'>
            <MenuIcon className='h-5 w-5' />
            <span className='sr-only'>Toggle navigation menu</span>
          </Button>
        </SheetTrigger>
        <SheetContent side='left' className='max-w-[320px]' {...({} as any)}>
          <nav className='grid gap-6 text-lg font-medium'>
            <SheetTrigger asChild>
              <Link
                to='#'
                className='flex items-center gap-2 text-lg font-semibold'
              >
                <div className='cursor-pointer w-[50px] h-[50px] bg-white rounded-sm backdrop-blur-sm'>
                  <img
                    src={logo}
                    alt=''
                    style={{
                      filter: 'drop-shadow(0px 0px 3px white)',
                    }}
                  />
                </div>
                <span className='text-muted-foreground transition-colors hover:text-foreground'>
                  SmartSecHub 🇺🇦
                </span>
              </Link>
            </SheetTrigger>
            <div className='flex gap-4'>
              <SheetTrigger asChild>
                <AppTheme className='w-[50%]' />
              </SheetTrigger>
              <SheetTrigger asChild>
                <ShareButton className='w-[50%]' />
              </SheetTrigger>
            </div>
            {isAuthorized && (
              <SheetTrigger asChild>
                <ProposalModal trigger={addResourceButton} />
              </SheetTrigger>
            )}
          </nav>
        </SheetContent>
      </Sheet>
      <nav className='font-medium flex items-center gap-5 text-sm lg:gap-6 flex-grow'>
        <div
          onClick={onLogoClick}
          className='cursor-pointer w-[50px] h-[50px] rounded-sm backdrop-blur-sm bg-white drop-shadow-lg'
        >
          <img src={logo} alt='' />
        </div>
        <span className='text-muted-foreground transition-colors hover:text-foreground'>
          SmartSecHub 🇺🇦
        </span>
        <WelcomeModal />
      </nav>
      <div className='ml-auto md:flex gap-2 items-center hidden'>
        <AppTheme />
        <ShareButton />
        {isAuthorized && <ProposalModal trigger={addResourceButton} />}
        {!isAuthorized && <SignInButtons />}
        {isAuthorized && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant='secondary'
                size='icon'
                className='rounded-full ml-auto'
              >
                <CircleUserIcon className='h-5 w-5' />
              </Button>
            </DropdownMenuTrigger>

            <DropdownMenuContent align='end'>
              {hasAdminRole && (
                <DropdownMenuItem onClick={() => navigateTo('/admin')}>
                  Admin
                </DropdownMenuItem>
              )}
              <DropdownMenuItem onClick={logout}>Logout</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>
    </header>
  );
};
