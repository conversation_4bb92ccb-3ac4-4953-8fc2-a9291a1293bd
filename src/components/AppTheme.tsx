import { memo } from "react";
import { useDarkMode } from "../hooks/useDarkMode";

import { MoonStar, Sun } from "lucide-react";
import { AppButtonSecondary } from "./buttons/app-button-new";

export const AppTheme = memo(({ className }: { className?: string }) => {
	const { isDarkMode, toggle } = useDarkMode(true);

	return (
		<AppButtonSecondary onClick={toggle} className={className}>
			{isDarkMode ? <Sun /> : <MoonStar />}
		</AppButtonSecondary>
	);
});
